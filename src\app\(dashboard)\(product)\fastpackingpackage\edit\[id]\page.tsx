'use client'

import { useParams } from 'next/navigation'
import { useState, useEffect } from 'react'
import { PackageFormData } from '@/types/package-form'
import { EditTabs } from '@/modules/product/component/edit-tabs'
import { PackageDetailsForm } from '@/modules/product/component/package-detail-form'

export default function EditDetailsPage({ params }: EditItineraryPageProps) {
  const { id } = use(params)
      const packageId = parseInt(id)

  const [formData, setFormData] = useState<PackageFormData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function fetchData() {
      // fetch data or use props/api
      const demoData: PackageFormData = {
        name: 'Everest Base Camp Trek',
        slug: 'everest-base-camp-trek',
        regionId: 'everest',
        activityId: 'fastpackingpackage',
        accomodation: 'Tea House, Lodge',
        distance: '130 km',
        type: 'Mountain Trail',
        duration: '13 Nights 14 Days',
        altitude: '5,364 m | 17,598 ft',
        meals: 'Breakfast, Lunch & Dinner',
        groupSize: '2-12',
        price: '$1,299',
        discountPrice: '$1,199',
        bestSeason: 'March-May / September-November',
        transport: 'Flight / Jeep',
        activityPerDay: '6-8 hrs',
        grade: 'Moderate to Challenging',
        bookingLink: '',
        overviewDescription: '',
        thumbnail: '',
        mainImage: '',
        mainImageAlt: '',
        pdfBrochure: '',
        published: true,
        tripOftheMonth: true,
        popularTour: false,
        shortTrek: false,
      }
      setFormData(demoData)
      setLoading(false)
    }
    fetchData()
  }, [packageId])

  if (loading || !formData) return <p>Loading...</p>

  return (
    <div>
      <EditTabs packageId={packageId} />
      <PackageDetailsForm formData={formData} onFormDataChange={setFormData} />
    </div>
  )
}



// 'use client'

// import { useRouter } from 'next/navigation'
// import PackagePage from '@/modules/product/template/package-page'
// import { use } from 'react'

// interface EditFastPackingPackagePageProps {
//   params: Promise<{ id: string }>
// }

// export default function EditFastPackingPackagePage({ params }: EditFastPackingPackagePageProps) {
//   const router = useRouter()

//   const { id } = use(params)
//   const packageId: number = parseInt(id)

//   const handleCancel = (): void => {
//     router.push(`/fastpackingpackage`)
//   }

//   return (
//     <PackagePage
//       mode="edit"
//       category="fastpackingpackage"
//       packageId={packageId}
//       // initialData={demoPackageData}
//       // onSave={handleSave}
//       onCancel={handleCancel}
//     />
//   )
// }
