import React from 'react'
import { UseGetHero } from '../hero/queries/use-get-home';
import { useRouter } from 'next/navigation';
import Image from 'next/image';

const HeroSection = () => {

    const { data, isLoading, error } = UseGetHero();
    const router = useRouter();

    if (isLoading) return <p>Loading hero...</p>;
    if (error) return <p>Error loading hero: {error.message}</p>;

    const hero = data?.data;

    return (
        <div>
            <section className='p-3'>
                <div className="px-6 py-6 bg-gray-200 rounded-lg">
                    <div className="flex items-center justify-between mb-6">
                        <h1 className="text-3xl font-bold">Hero Section</h1>
                        <button
                            className="bg-black text-white px-4 py-2 rounded hover:bg-gray-900 transition"
                            onClick={() => router.push("/home/<USER>/edit")}
                        >
                            Edit
                        </button>
                    </div>
                    <div className="bg-white rounded-lg shadow">
                        <table className="min-w-full text-left border-separate border-spacing-0">
                            <thead>
                                <tr>
                                    <th className="py-3 px-4 text-gray-500 font-semibold">SN</th>
                                    <th className="py-3 px-4 text-gray-500 font-semibold">Titles</th>
                                    <th className="py-3 px-4 text-gray-500 font-semibold">Subtitles</th>
                                    <th className="py-3 px-4 text-gray-500 font-semibold">Images</th>
                                    <th className="py-3 px-4 text-gray-500 font-semibold">Video</th>
                                    <th className="py-3 px-4 text-gray-500 font-semibold">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr className="border-t last:border-b hover:bg-gray-100 transition">
                                    <td className="py-3 px-4">1</td>
                                    <td className="py-3 px-4">
                                        {hero?.titles && hero.titles.length > 0
                                            ? hero.titles.map((title, i) => (
                                                <div key={i}>{title}</div>
                                            ))
                                            : "-"}
                                    </td>
                                    <td className="py-3 px-4">
                                        {hero?.subtitles && hero.subtitles.length > 0
                                            ? hero.subtitles.map((subtitle, i) => (
                                                <div key={i}>{subtitle}</div>
                                            ))
                                            : "-"}
                                    </td>
                                    <td className="py-3 px-4">
                                        {hero?.images && hero.images.length > 0 ? (
                                            <div className="flex gap-2">
                                                {hero.images.map((img, i) => (
                                                    <Image
                                                        key={i}
                                                        src={img}
                                                        alt={`Hero image ${i}`}
                                                        width={60}
                                                        height={40}
                                                        className="rounded object-cover"
                                                        style={{ width: '60px', height: '40px' }}
                                                    />
                                                ))}
                                            </div>
                                        ) : (
                                            "-"
                                        )}
                                    </td>
                                    <td className="py-3 px-4">
                                        {hero?.videoUrl ? (
                                            <video
                                                src={hero.videoUrl}
                                                controls
                                                width={80}
                                                height={48}
                                                className="rounded"
                                                style={{ objectFit: "cover" }}
                                                preload="metadata"
                                            />
                                        ) : (
                                            "-"
                                        )}
                                    </td>
                                    <td className="py-3 px-4 flex gap-2">
                                        <button
                                            className="flex items-center gap-1 px-3 py-1 bg-black text-white rounded hover:bg-gray-800 transition text-sm"
                                            onClick={() => router.push("/home/<USER>/edit")}
                                        >
                                            Edit
                                        </button>
                                        <button
                                            className="flex items-center gap-1 px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition text-sm"
                                        >
                                            Delete
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>
        </div>
    )
}

export default HeroSection