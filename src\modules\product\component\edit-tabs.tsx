'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'

interface EditTabsProps {
    packageId: string | number
}

export function EditTabs({ packageId }: EditTabsProps) {
    const pathname = usePathname()
    const basePath = `/package/${packageId}/edit`

    const tabs = [
        { key: 'details', label: 'Details', href: `${basePath}` },
        { key: 'itinerary', label: 'Itinerary', href: `${basePath}/itinerary` },
        { key: 'equipment', label: 'Equipment', href: `${basePath}/equipment` },
    ]

    return (
        <nav className="flex space-x-4 border-b mb-4">
            {tabs.map(tab => {
                const isActive = pathname === tab.href
                return (
                    <Link key={tab.key} href={tab.href}>
                        <p className={`px-4 py-2 ${isActive ? 'border-b-2 border-blue-600 font-semibold text-blue-600' : 'text-gray-600'}`}>
                            {tab.label}
                        </p>
                    </Link>
                )
            })}
        </nav>
    )
}
