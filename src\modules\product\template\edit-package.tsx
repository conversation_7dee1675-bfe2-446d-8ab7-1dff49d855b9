'use client'

import { useEffect, useState } from 'react'
import { CollapsibleSection } from '../component/collapsible'
import { PackageDetailsForm } from '../component/package-detail-form'
import { SeoDetailsSection } from '@/components/package/seo-detail-section'
import { SchemaDetailsSection } from '@/components/package/seo-schema-detail-section'
import { PackageFormData } from '@/types/package'
import { Button } from '@/components/ui/button'

interface PackageEditPageProps {
  packageId: string
  initialData?: PackageFormData
  onSave?: (data: PackageFormData) => void
  onCancel?: () => void
  fetchPackageData?: (id: string) => Promise<PackageFormData> // optional external fetcher
}

export default function PackageEditPage({
  packageId,
  initialData,
  onSave,
  onCancel,
  fetchPackageData
}: PackageEditPageProps) {
  const [packageData, setPackageData] = useState<PackageFormData | null>(initialData || null)
  const [loading, setLoading] = useState<boolean>(!initialData)
  const [seoOpen, setSeoOpen] = useState(true)
  const [schemaOpen, setSchemaOpen] = useState(false)
  const [packageEditOpen, setPackageEditOpen] = useState(true)

  // If no initial data is passed, fetch from API
  useEffect(() => {
    if (!initialData && fetchPackageData) {
      setLoading(true)
      fetchPackageData(packageId).then((data) => {
        setPackageData(data)
        setLoading(false)
      })
    }
  }, [initialData, fetchPackageData, packageId])

  const handleSave = () => {
    if (!packageData) return
    if (onSave) {
      onSave(packageData)
    } else {
      console.log('Updating package data:', packageData)
      // TODO: Implement actual update logic here (API call)
    }
  }

  if (loading || !packageData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p className="text-gray-600">Loading package data...</p>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="container mx-auto space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-900">Edit Package</h1>
        </div>

        {/* SEO section */}
        <CollapsibleSection
          title="SEO Details"
          isOpen={seoOpen}
          onToggle={() => setSeoOpen(!seoOpen)}
        >
          <SeoDetailsSection
            formData={{
              metaTitle: packageData.name,
              metaDescription: packageData.overviewDescription,
              metaKeywords: '',
              canonicalUrl: ''
            }}
            setFormData={(seoFields) => {
              setPackageData((prev) => prev ? { ...prev, ...seoFields } : null)
            }}
          />
        </CollapsibleSection>

        {/* Schema section */}
        <CollapsibleSection
          title="Schema Details"
          isOpen={schemaOpen}
          onToggle={() => setSchemaOpen(!schemaOpen)}
        >
          <SchemaDetailsSection
            schema={packageData.slug || ''}
            setSchema={(schema) =>
              setPackageData((prev) => prev ? { ...prev, schema } : null)
            }
          />
        </CollapsibleSection>

        {/* Package form section */}
        <CollapsibleSection
          title="Edit Package Details"
          isOpen={packageEditOpen}
          onToggle={() => setPackageEditOpen(!packageEditOpen)}
        >
          <PackageDetailsForm
            formData={packageData}
            onFormDataChange={(updatedData) => setPackageData(updatedData)}
          />
        </CollapsibleSection>

        {/* Action buttons */}
        <div className="flex justify-end gap-2">
          <Button
            onClick={onCancel}
            variant="outline"
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Cancel
          </Button>

          <Button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Save Changes
          </Button>
        </div>
      </div>
    </div>
  )
}
