"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { UseGetExperience } from "../experience/queries/use-get-experience";

const ExperienceDisplay: React.FC = () => {
  const { data, isLoading, error } = UseGetExperience();
  const router = useRouter();

  if (isLoading) return <p>Loading experience...</p>;
  if (error) return <p>Error loading experience: {error.message}</p>;

  const experience = data?.data;

  return (
    <div className="p-6 container mx-auto text-center">
      {experience ? (
        <div className="px-6 py-6 bg-gray-200 rounded-lg">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-3xl font-bold">{experience.heading}</h1>
            <button
              className="bg-black text-white px-4 py-2 rounded hover:bg-gray-900 transition"
              onClick={() => router.push("/home/<USER>/edit")}
            >
              Edit Experience
            </button>
          </div>

          {experience.subHeading && (
            <p className="mb-6">{experience.subHeading}</p>
          )}

          <div className="bg-white rounded-lg shadow">
            <table className="min-w-full border border-gray-300 mb-6 text-left">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border px-4 py-2">SN</th>
                  <th className="border px-4 py-2">Feature Title</th>
                  <th className="border px-4 py-2">Feature Subtitle</th>
                </tr>
              </thead>
              <tbody>
                {experience.features.map((feature, idx) => (
                  <tr key={idx} className="even:bg-gray-50">
                    <td className="border px-4 py-2">{idx + 1}</td>
                    <td className="border px-4 py-2">{feature.title}</td>
                    <td className="border px-4 py-2">{feature.subtitle}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        <>
          <p className="mb-6">No experience data found.</p>
          <button
            className="bg-black text-white px-4 py-2 rounded hover:bg-gray-900 transition"
            onClick={() => router.push("/home/<USER>/create")}
          >
            Create Experience
          </button>
        </>
      )}
    </div>
  );
};

export default ExperienceDisplay;
