'use client';

import React, { useState, useEffect, ChangeEvent } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { UseUpdateHero } from '../mutations/use-update-hero';
import { useGetHome } from '../../queries/get-home';
import { UseGetHero } from '../queries/use-get-home';

const EditHeroPage: React.FC = () => {
  const { data: homeData } = useGetHome();
  const { data, isLoading, error } = UseGetHero();
  const updateHero = UseUpdateHero();

  const router = useRouter();

  const [videoUrl, setVideoUrl] = useState<string>('');
  const [titles, setTitles] = useState<string[]>(['']);
  const [subtitles, setSubtitles] = useState<string[]>(['']);
  const [images, setImages] = useState<string[]>([]);
  const [homeId, setHomeId] = useState<string>('');
  const [heroId, setHeroId] = useState<string>('');

  useEffect(() => {
    if (homeData?.data?.id) {
      setHomeId(homeData.data.id);
    }
  }, [homeData]);

  useEffect(() => {
    if (data?.data) {
      const hero = data.data;
      setHeroId(hero.id);
      setVideoUrl(hero.videoUrl);
      setTitles(hero.titles.length ? hero.titles : ['']);
      setSubtitles(hero.subtitles.length ? hero.subtitles : ['']);
      setImages(hero.images);
      if (!hero.homeId && homeId) {
        // Optionally update homeId state if hero misses it
        setHomeId(homeId);
      }
    }
  }, [data, homeId]);

  const handleVideoUrlChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files) return;
    const formdata = new FormData();
    formdata.append('file', e.target.files[0]);
    fetch('https://api.trailandtreknepal.com/image/upload', {
      method: 'POST',
      body: formdata,
    })
      .then((response) => response.json())
      .then((data) => {
        setVideoUrl(data.data.url);
      });
  };

  const handleTitleChange = (index: number, value: string) => {
    const newTitles = [...titles];
    newTitles[index] = value;
    setTitles(newTitles);
  };

  const handleAddTitle = () => {
    setTitles([...titles, '']);
  };

  const handleRemoveTitle = (index: number) => {
    if (titles.length > 1) {
      setTitles(titles.filter((_, i) => i !== index));
    }
  };

  const handleSubtitleChange = (index: number, value: string) => {
    const newSubtitles = [...subtitles];
    newSubtitles[index] = value;
    setSubtitles(newSubtitles);
  };

  const handleAddSubtitle = () => {
    setSubtitles([...subtitles, '']);
  };

  const handleRemoveSubtitle = (index: number) => {
    if (subtitles.length > 1) {
      setSubtitles(subtitles.filter((_, i) => i !== index));
    }
  };

  const handleImageUpload = (e: ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files) return;
    for (const file of e.target.files) {
      const formData = new FormData();
      formData.append('file', file);
      console.log('Uploading image:', file);
      fetch('https://api.trailandtreknepal.com/image/upload', {
        method: 'POST',
        body: formData,
      })
        .then((response) => response.json())
        .then((data) => {
          console.log('Image uploaded:', data);
          setImages((prev) => [...prev, data.data.url]);
        });
    }
  };

  const handleRemoveImage = (index: number) => {
    setImages(images.filter((_, i) => i !== index));
  };

  const handleUpdate = async () => {
    const validTitles = titles.filter((title) => title.trim());
    if (validTitles.length === 0) {
      alert('Please enter at least one title');
      return;
    }
    if (!homeId) {
      alert('Home ID is loading or missing, please try again later.');
      return;
    }
    if (!data?.data) {
      alert('Hero data is not loaded, please try again later.');
      return;
    }

    const heroData = {
      id: heroId,
      homeId: homeId,
      videoUrl: videoUrl.trim(),
      titles: validTitles,
      subtitles: subtitles.filter((subtitle) => subtitle.trim()),
      images: images,
      createdAt: data.data.createdAt,
      updatedAt: data.data.updatedAt,
    };

    try {
      await updateHero.mutateAsync(heroData);
      router.push('/home/<USER>');
    } catch (error) {
      console.error('Failed to update hero:', error);
    }
  };

  const hasValidTitle = titles.some((title) => title.trim());

  if (isLoading && !heroId) return <p>Loading hero data...</p>;
  if (error) return <p>Error loading hero: {error.message}</p>;

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h2 className="text-2xl font-semibold mb-4">Edit Hero</h2>

      <div className="mb-6">
        <label className="block mb-1 font-medium">Video URL</label>
        <input
          type="file"
          accept="video/*"
          className="w-full border rounded px-3 py-2"
          onChange={handleVideoUrlChange}
          disabled={isLoading}
        />
        {videoUrl && (
          <div className="mt-4">
            <video
              className="w-full h-64 rounded"
              src={videoUrl}
              title="Preview Video"
              controls
              // frameBorder="0"
              // allowFullScreen
            />
          </div>
        )}
      </div>

      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <label className="block font-medium">Titles *</label>
          <button
            type="button"
            onClick={handleAddTitle}
            disabled={isLoading}
            className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400"
          >
            Add Title
          </button>
        </div>
        {titles.map((title, index) => (
          <div key={index} className="flex items-center gap-2 mb-2">
            <input
              type="text"
              className="flex-1 border rounded px-3 py-2"
              value={title}
              onChange={(e) => handleTitleChange(index, e.target.value)}
              placeholder={`Enter title ${index + 1}`}
              disabled={isLoading}
            />
            {titles.length > 1 && (
              <button
                type="button"
                onClick={() => handleRemoveTitle(index)}
                disabled={isLoading}
                className="px-2 py-2 text-red-600 hover:bg-red-50 rounded disabled:text-gray-400"
              >
                ✕
              </button>
            )}
          </div>
        ))}
      </div>

      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <label className="block font-medium">Subtitles</label>
          <button
            type="button"
            onClick={handleAddSubtitle}
            disabled={isLoading}
            className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400"
          >
            Add Subtitle
          </button>
        </div>
        {subtitles.map((subtitle, index) => (
          <div key={index} className="flex items-center gap-2 mb-2">
            <input
              type="text"
              className="flex-1 border rounded px-3 py-2"
              value={subtitle}
              onChange={(e) => handleSubtitleChange(index, e.target.value)}
              placeholder={`Enter subtitle ${index + 1}`}
              disabled={isLoading}
            />
            {subtitles.length > 1 && (
              <button
                type="button"
                onClick={() => handleRemoveSubtitle(index)}
                disabled={isLoading}
                className="px-2 py-2 text-red-600 hover:bg-red-50 rounded disabled:text-gray-400"
              >
                ✕
              </button>
            )}
          </div>
        ))}
      </div>

      <div className="mb-6">
        <label className="block mb-1 font-medium">Images</label>
        <input
          type="file"
          accept="image/*"
          multiple
          onChange={handleImageUpload}
          disabled={isLoading}
        />
        <div className="mt-4 grid grid-cols-3 gap-4">
          {images.map((src, idx) => (
            <div key={idx} className="relative">
              <Image
                src={src}
                alt={`Uploaded ${idx}`}
                width={150}
                height={100}
                className="w-full h-32 object-cover rounded"
              />
              <button
                onClick={() => handleRemoveImage(idx)}
                className="absolute top-1 right-1 text-white bg-black bg-opacity-50 rounded-full p-1"
                disabled={isLoading}
              >
                ✕
              </button>
            </div>
          ))}
        </div>
      </div>

      <div className="mt-6 flex space-x-2">
        <button
          onClick={handleUpdate}
          disabled={isLoading || !hasValidTitle}
          className={`px-4 py-2 rounded text-white ${
            isLoading || !hasValidTitle
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-green-600 hover:bg-green-700'
          }`}
        >
          {isLoading ? 'Updating...' : 'Update'}
        </button>
        <Link href="/home/<USER>">
          <button
            className="px-4 py-2 bg-gray-400 text-white rounded hover:bg-gray-500"
            disabled={isLoading}
          >
            Cancel
          </button>
        </Link>
      </div>
    </div>
  );
};

export default EditHeroPage;
